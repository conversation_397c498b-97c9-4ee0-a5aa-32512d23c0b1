// backend/src/services/replicateService.js
const Replicate = require('replicate');

// Ensure REPLICATE_API_TOKEN is loaded (dotenv should handle this from .env)
if (!process.env.REPLICATE_API_TOKEN) {
  console.error("FATAL ERROR: REPLICATE_API_TOKEN is not defined in the environment variables.");
  // Consider throwing an error or exiting if this is critical for startup
  // throw new Error("REPLICATE_API_TOKEN is not defined.");
}

const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN,
});

// Default model version - user MUST replace this in .env or provide it dynamically
const DEFAULT_WHISPER_MODEL_VERSION = "REPLACE_WITH_ACTUAL_WHISPER_MODEL_ON_REPLICATE";
// Example: "openai/whisper:4d50797290df275329f8b422a0aaef5535eb09d590a141599e049950ddfd1002" (a general Whisper model)
// Or a specific WhisperX variant if available on Replicate: "owner/whisperx-model:versionhash"

/**
 * Starts a transcription job on Replicate using file path.
 * @param {string} filePath - Local file path to the audio/video file.
 * @param {string} [language=null] - Language code (e.g., "en", "es"). Null for auto-detect.
 * @param {string} [model='large-v2'] - Whisper model size (e.g., "large-v2", "medium", "base").
 * @returns {Promise<object>} The Replicate prediction object.
 */
exports.startTranscription = async (filePath, language = null, model = 'large-v2') => {
  const replicateModelVersion = process.env.REPLICATE_WHISPER_MODEL_VERSION || DEFAULT_WHISPER_MODEL_VERSION;

  if (replicateModelVersion === DEFAULT_WHISPER_MODEL_VERSION) {
    const warningMessage = "Warning: REPLICATE_WHISPER_MODEL_VERSION is not configured or is using the default placeholder. Transcription will likely fail. Please set it in your .env file.";
    console.warn(warningMessage);
    // Optionally, throw an error to prevent API calls with a bad model string:
    // throw new Error(warningMessage);
  }

  console.log(`Starting Replicate transcription for file: ${filePath}`);
  console.log(`Using Replicate model version: ${replicateModelVersion}`);

  // Check file size and choose appropriate upload method
  const fs = require('fs').promises;
  const path = require('path');

  if (!require('fs').existsSync(filePath)) {
    throw new Error(`File not found: ${filePath}`);
  }

  // Get file stats to check size
  const stats = await fs.stat(filePath);
  const fileSizeInMB = stats.size / (1024 * 1024);

  console.log(`File size: ${fileSizeInMB.toFixed(2)} MB`);

  let audioFilePath = filePath;
  let shouldProcessInChunks = false;
  let chunkPaths = [];

  // If file is too large or is a video, extract audio using FFmpeg
  if (fileSizeInMB > 100 || path.extname(filePath).toLowerCase() === '.mp4') {
    console.log(`File is ${fileSizeInMB.toFixed(2)} MB or is a video. Extracting audio with FFmpeg...`);

    const audioProcessingService = require('./audioProcessingService');
    audioFilePath = await audioProcessingService.extractAudio(filePath);

    // Check the size of the extracted audio
    const audioStats = await fs.stat(audioFilePath);
    const audioSizeInMB = audioStats.size / (1024 * 1024);
    console.log(`Extracted audio size: ${audioSizeInMB.toFixed(2)} MB`);

    // If extracted audio is still too large, split into chunks
    if (audioSizeInMB > 80) { // Use 80MB threshold to leave buffer
      console.log(`Audio file is still large (${audioSizeInMB.toFixed(2)} MB). Splitting into chunks...`);
      chunkPaths = await audioProcessingService.splitAudioIntoChunks(audioFilePath, 10, 80);
      shouldProcessInChunks = true;
    }
  }

  // Process either single file or chunks
  if (shouldProcessInChunks) {
    return await exports.processAudioChunks(chunkPaths, language, model);
  } else {
    const fileBuffer = await fs.readFile(audioFilePath);
    return await exports.processSingleAudioFile(fileBuffer, language, model);
  }
};

/**
 * Processes a single audio file with Replicate.
 * @param {Buffer} fileBuffer - The audio file as a Buffer.
 * @param {string} language - Language code.
 * @param {string} model - Model name.
 * @returns {Promise<object>} Transcription result.
 */
exports.processSingleAudioFile = async (fileBuffer, language, model) => {
  const replicateModelVersion = process.env.REPLICATE_WHISPER_MODEL_VERSION || DEFAULT_WHISPER_MODEL_VERSION;

  const input = {
    audio_file: fileBuffer,
    align_output: true, // Essential for word-level timestamps (WhisperX specific)
  };

  if (language) {
    input.language = language;
  }

  try {
    const options = { input };

    if (process.env.REPLICATE_WEBHOOK_URL) {
      options.webhook = process.env.REPLICATE_WEBHOOK_URL;
      options.webhook_events_filter = ["completed", "failed"];
    }

    const output = await replicate.run(replicateModelVersion, options);
    console.log(`Single file transcription completed successfully`);

    return {
      id: 'single-file-' + Date.now(),
      status: 'succeeded',
      output: output
    };
  } catch (error) {
    console.error('Error running single file transcription:', error.response ? error.response.data : error.message);
    throw error;
  }
};

/**
 * Processes multiple audio chunks with Replicate and combines results.
 * @param {Array<string>} chunkPaths - Array of paths to audio chunks.
 * @param {string} language - Language code.
 * @param {string} model - Model name.
 * @returns {Promise<object>} Combined transcription result.
 */
exports.processAudioChunks = async (chunkPaths, language, model) => {
  console.log(`Processing ${chunkPaths.length} audio chunks...`);

  const fs = require('fs').promises;
  const chunkResults = [];
  let totalDurationOffset = 0;

  for (let i = 0; i < chunkPaths.length; i++) {
    const chunkPath = chunkPaths[i];
    console.log(`Processing chunk ${i + 1}/${chunkPaths.length}: ${chunkPath}`);

    try {
      const chunkBuffer = await fs.readFile(chunkPath);
      const chunkResult = await exports.processSingleAudioFile(chunkBuffer, language, model);

      // Adjust timestamps to account for chunk position in original audio
      if (chunkResult.output && chunkResult.output.segments) {
        chunkResult.output.segments.forEach(segment => {
          segment.start += totalDurationOffset;
          segment.end += totalDurationOffset;

          // Adjust word-level timestamps if available
          if (segment.words) {
            segment.words.forEach(word => {
              word.start += totalDurationOffset;
              word.end += totalDurationOffset;
            });
          }
        });
      }

      chunkResults.push(chunkResult);

      // Update duration offset for next chunk (10 minutes per chunk)
      totalDurationOffset += 10 * 60; // 10 minutes in seconds

    } catch (error) {
      console.error(`Error processing chunk ${i + 1}:`, error);
      throw new Error(`Failed to process chunk ${i + 1}: ${error.message}`);
    }
  }

  // Combine all chunk results
  const combinedOutput = exports.combineChunkResults(chunkResults);

  console.log(`Successfully processed all ${chunkPaths.length} chunks`);

  return {
    id: 'chunked-' + Date.now(),
    status: 'succeeded',
    output: combinedOutput,
    chunks: chunkResults.length
  };
};

/**
 * Combines transcription results from multiple chunks into a single result.
 * @param {Array<object>} chunkResults - Array of chunk transcription results.
 * @returns {object} Combined transcription result.
 */
exports.combineChunkResults = (chunkResults) => {
  const combinedSegments = [];
  let combinedText = '';

  chunkResults.forEach((chunkResult, index) => {
    if (chunkResult.output && chunkResult.output.segments) {
      combinedSegments.push(...chunkResult.output.segments);
    }

    if (chunkResult.output && chunkResult.output.text) {
      if (combinedText) combinedText += ' ';
      combinedText += chunkResult.output.text;
    }
  });

  return {
    text: combinedText,
    segments: combinedSegments,
    language: chunkResults[0]?.output?.language || 'en',
    chunks_processed: chunkResults.length
  };
};

/**
 * Fetches the status and output of a Replicate transcription job.
 * @param {string} jobId - The ID of the Replicate prediction.
 * @returns {Promise<object>} The Replicate prediction object with current status and output.
 */
exports.getTranscriptionStatus = async (jobId) => {
  console.log(`Fetching status for Replicate job: ${jobId}`);
  try {
    const prediction = await replicate.predictions.get(jobId);
    return prediction;
  } catch (error) {
    console.error(`Error fetching Replicate job status for ${jobId}:`, error.response ? error.response.data : error.message);
    throw error; // Re-throw
  }
};
