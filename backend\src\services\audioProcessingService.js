// backend/src/services/audioProcessingService.js
const ffmpegPath = require('@ffmpeg-installer/ffmpeg').path;
const ffprobePath = require('@ffprobe-installer/ffprobe').path;
const ffmpeg = require('fluent-ffmpeg');
ffmpeg.setFfmpegPath(ffmpegPath);
ffmpeg.setFfprobePath(ffprobePath);

const path = require('path');
const fs = require('fs').promises; // Using promises version of fs

/**
 * Converts an audio/video file to a standard audio format (e.g., MP3, 16kHz mono WAV).
 * This might be useful if Replicate's Whisper model has specific input requirements.
 * @param {string} inputPath - Path to the input file.
 * @param {string} outputDir - Directory to save the converted file.
 * @param {string} originalFilename - Original name of the file for output naming.
 * @param {object} [options] - FFmpeg options (e.g., { format: 'wav', audioChannels: 1, audioFrequency: 16000 }).
 * @returns {Promise<string>} Path to the converted audio file.
 */
exports.convertToStandardAudio = (inputPath, outputDir, originalFilename, options = {}) => {
  return new Promise(async (resolve, reject) => {
    const outputFormat = options.format || 'mp3'; // Default to mp3
    const outputFilename = `${path.parse(originalFilename).name}_${Date.now()}.${outputFormat}`;
    const outputPath = path.join(outputDir, outputFilename);

    try {
      await fs.mkdir(outputDir, { recursive: true }); // Ensure output directory exists

      let command = ffmpeg(inputPath)
        .toFormat(outputFormat);

      if (options.audioCodec) command = command.audioCodec(options.audioCodec); // e.g., 'pcm_s16le' for WAV
      if (options.audioChannels) command = command.audioChannels(options.audioChannels); // e.g., 1 for mono
      if (options.audioFrequency) command = command.audioFrequency(options.audioFrequency); // e.g., 16000 for 16kHz
      if (options.audioBitrate) command = command.audioBitrate(options.audioBitrate); // e.g., '128k' for MP3

      command
        .on('start', (commandLine) => {
          console.log('FFmpeg command started: ' + commandLine);
        })
        .on('end', () => {
          console.log(`Audio conversion finished: ${outputPath}`);
          resolve(outputPath);
        })
        .on('error', (err, stdout, stderr) => {
          console.error(`Error during audio conversion: ${err.message}`);
          console.error('FFmpeg stdout:', stdout);
          console.error('FFmpeg stderr:', stderr);
          reject(err);
        })
        .save(outputPath);
    } catch (err) {
      console.error(`Error setting up FFmpeg conversion: ${err.message}`);
      reject(err);
    }
  });
};

/**
 * Extracts audio from a video file.
 * @param {string} videoPath - Path to the video file.
 * @param {string} outputDir - Directory to save the extracted audio file.
 * @param {string} originalFilename - Original name of the file for output naming.
 * @param {string} [audioFormat='mp3'] - Desired audio format for extraction.
 * @returns {Promise<string>} Path to the extracted audio file.
 */
exports.extractAudioFromVideo = (videoPath, outputDir, originalFilename, audioFormat = 'mp3') => {
  return new Promise(async (resolve, reject) => {
    const outputFilename = `audio_from_${path.parse(originalFilename).name}_${Date.now()}.${audioFormat}`;
    const outputPath = path.join(outputDir, outputFilename);

    try {
      await fs.mkdir(outputDir, { recursive: true });

      ffmpeg(videoPath)
        .noVideo() // Disable video recording
        .toFormat(audioFormat)
        // .audioCodec('libmp3lame') // Example for MP3, adjust if needed
        // .audioBitrate('192k')    // Example bitrate
        .on('start', (commandLine) => {
          console.log('FFmpeg audio extraction started: ' + commandLine);
        })
        .on('end', () => {
          console.log(`Audio extraction finished: ${outputPath}`);
          resolve(outputPath);
        })
        .on('error', (err, stdout, stderr) => {
          console.error(`Error during audio extraction: ${err.message}`);
          console.error('FFmpeg stdout:', stdout);
          console.error('FFmpeg stderr:', stderr);
          reject(err);
        })
        .save(outputPath);
    } catch (err) {
      console.error(`Error setting up FFmpeg audio extraction: ${err.message}`);
      reject(err);
    }
  });
};

/**
 * Gets media file metadata using ffprobe.
 * @param {string} filePath - Path to the media file.
 * @returns {Promise<object>} Metadata object from ffprobe.
 */
exports.getMediaMetadata = (filePath) => {
  return new Promise((resolve, reject) => {
    ffmpeg.ffprobe(filePath, (err, metadata) => {
      if (err) {
        console.error(`Error probing file ${filePath}:`, err.message);
        return reject(err);
      }
      resolve(metadata); // metadata object contains format, streams, etc.
    });
  });
};

/**
 * Extracts audio from a video file for Replicate transcription.
 * Uses optimized settings to reduce file size while maintaining quality.
 * @param {string} inputPath - Path to the input video file.
 * @returns {Promise<string>} Path to the extracted audio file.
 */
exports.extractAudio = async (inputPath) => {
  const path = require('path');
  const outputDir = path.dirname(inputPath); // Save in same directory as input
  const originalFilename = path.basename(inputPath);

  // Use MP3 format with optimized settings for transcription
  // MP3 is widely supported and provides good compression
  return await exports.extractAudioFromVideo(inputPath, outputDir, originalFilename, 'mp3');
};

/**
 * Splits an audio file into smaller chunks for processing.
 * @param {string} audioPath - Path to the audio file to split.
 * @param {number} [chunkDurationMinutes=10] - Duration of each chunk in minutes.
 * @param {number} [maxChunkSizeMB=80] - Maximum size of each chunk in MB (leaving buffer under 100MB limit).
 * @returns {Promise<Array<string>>} Array of paths to the audio chunks.
 */
exports.splitAudioIntoChunks = async (audioPath, chunkDurationMinutes = 10, maxChunkSizeMB = 80) => {
  const path = require('path');
  const fs = require('fs').promises;

  const outputDir = path.dirname(audioPath);
  const baseName = path.parse(audioPath).name;
  const chunkPaths = [];

  try {
    // Get audio metadata to determine total duration
    const metadata = await exports.getMediaMetadata(audioPath);
    const totalDurationSeconds = metadata.format.duration;
    const chunkDurationSeconds = chunkDurationMinutes * 60;

    console.log(`Audio duration: ${(totalDurationSeconds / 60).toFixed(2)} minutes`);
    console.log(`Splitting into ${chunkDurationMinutes}-minute chunks...`);

    // Calculate number of chunks needed
    const numChunks = Math.ceil(totalDurationSeconds / chunkDurationSeconds);

    for (let i = 0; i < numChunks; i++) {
      const startTime = i * chunkDurationSeconds;
      const chunkFilename = `${baseName}_chunk_${i + 1}_of_${numChunks}.mp3`;
      const chunkPath = path.join(outputDir, chunkFilename);

      await new Promise((resolve, reject) => {
        ffmpeg(audioPath)
          .seekInput(startTime) // Start at specific time
          .duration(chunkDurationSeconds) // Duration of chunk
          .toFormat('mp3')
          .audioBitrate('128k') // Optimize for size while maintaining quality
          .audioChannels(1) // Mono to reduce file size
          .audioFrequency(22050) // Reduce sample rate for smaller files
          .on('start', (commandLine) => {
            console.log(`Creating chunk ${i + 1}/${numChunks}: ${chunkFilename}`);
            console.log(`FFmpeg command: ${commandLine}`);
          })
          .on('end', () => {
            console.log(`Chunk ${i + 1} completed: ${chunkPath}`);
            resolve();
          })
          .on('error', (err) => {
            console.error(`Error creating chunk ${i + 1}:`, err.message);
            reject(err);
          })
          .save(chunkPath);
      });

      // Verify chunk size
      const chunkStats = await fs.stat(chunkPath);
      const chunkSizeMB = chunkStats.size / (1024 * 1024);
      console.log(`Chunk ${i + 1} size: ${chunkSizeMB.toFixed(2)} MB`);

      if (chunkSizeMB > maxChunkSizeMB) {
        console.warn(`Warning: Chunk ${i + 1} (${chunkSizeMB.toFixed(2)} MB) exceeds target size of ${maxChunkSizeMB} MB`);
      }

      chunkPaths.push(chunkPath);
    }

    console.log(`Successfully created ${numChunks} audio chunks`);
    return chunkPaths;

  } catch (error) {
    console.error('Error splitting audio into chunks:', error);
    throw error;
  }
};

// Consider adding functions based on FFmpeg_settings.md if specific pre-processing
// is required before sending to Replicate (e.g., normalize audio, change sample rate).
// However, many Replicate models handle common formats well.

